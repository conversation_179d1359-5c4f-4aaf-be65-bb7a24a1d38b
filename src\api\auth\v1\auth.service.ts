import { User } from "@api/users/user.model";
import { BaseService } from "@base/BaseService";
import { PERMISSION_TYPE } from "@constances/enum";
import env from "@env";
import { ISignInInterface, IToken, ITokenPayload } from "@interfaces/authen.interface";
import { BadRequestError, EncUtil, NotFoundError } from "common";
import jwt from "jsonwebtoken";
import { UserRepository } from "src/repositories/UserRepository";
export default class AuthService extends BaseService<User> {
	private static instance: AuthService;

	constructor(
		userRepository: UserRepository,
	) {
		super(userRepository);
	}

	public async signIn(signInBody: ISignInInterface): Promise<IToken> {
		const existingUser = await this.repository.findOne({
			where: {
				email: signInBody.email
			},
		});
		if (existingUser === null || existingUser.locked) {
			throw new NotFoundError('email_not_found');
		}

		const isValidPassword = await EncUtil.comparePassword(
			signInBody.password,
			existingUser.password!,
		);

		if (!isValidPassword) {
			throw new BadRequestError('the_password_is_in_correct');
		}

		const token = this.generateToken({
			id: existingUser.id,
			permission: PERMISSION_TYPE.User,
		});

		return {
			access_token: token.access_token,
			refresh_token: token.refresh_token,
		};
	}

	public getToken(
		user: ITokenPayload,
		expiresIn: string,
		secret: string,
	): string {
		return jwt.sign(
			{
				id: user.id,
				permission: user.permission,
			},
			secret,
			{
				expiresIn,
			},
		);
	}

	public decode(token: string): ITokenPayload {
		return jwt.decode(token) as unknown as ITokenPayload;
	}

	public refreshToken(refreshToken: string): IToken {
		const payload: ITokenPayload = jwt.verify(
			refreshToken,
			env.app.refreshTokenSecret,
		) as ITokenPayload;
		const token = this.generateToken(payload);
		return token;
	}

	public generateToken(payload: ITokenPayload): IToken {
		const access_token = this.getToken(
			{
				id: payload.id,
				permission: payload.permission,
			},
			env.app.jwtExpiredIn,
			env.app.jwtSecret,
		);

		const newRefreshToken = this.getToken(
			{
				id: payload.id,
				permission: payload.permission,
			},
			env.app.refreshTokenExpiredIn,
			env.app.refreshTokenSecret,
		);

		return {
			access_token,
			refresh_token: newRefreshToken,
		};
	}

	static getInstance(userRepository: UserRepository): AuthService {
		if (!AuthService.instance) {
			AuthService.instance = new AuthService(userRepository);
		}
		return AuthService.instance;
	}
}
