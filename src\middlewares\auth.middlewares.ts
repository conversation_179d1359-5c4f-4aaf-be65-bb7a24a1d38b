import { Admin } from '@api/admin/admin.model';
import { User } from '@api/users/user.model';
import env from '@env';
import {
	BadRequestError,
	TokenExpiredError,
	TokenInvalidError,
	NotAuthorizedError,
	ForbiddenError,
} from 'common';
import { ITokenPayload } from '@interfaces/authen.interface';
import axios from 'axios';
import { NextFunction, Request, Response } from 'express';
import jwt, { JwtPayload } from 'jsonwebtoken';
import { PERMISSION_TYPE } from '@constances/enum';

export const verifyTokenSystem = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const { authorization } = req.headers;
		const accessToken = req.headers['access-token-from-line'] as string;
		if (!authorization && !accessToken) {
			throw new ForbiddenError('missing authentication token');
		}
		if (authorization) {
			let data = null;
			const tokenWithoutBearer = authorization.replace('Bearer ', '');
			data = jwt.verify(tokenWithoutBearer, env.app.jwtSecret);
			const payload = data as JwtPayload;

			let currentUser: any = null;

			switch (payload.permission) {
				case PERMISSION_TYPE.Admin:
					currentUser = await Admin.findById(payload.id);
					break;
				case PERMISSION_TYPE.User:
					currentUser = await User.findById(payload.id);
					if (currentUser?.locked) currentUser = null;
					break;
				default:
					throw new NotAuthorizedError('unauthorized');
			}

			if (!currentUser) {
				throw new NotAuthorizedError('unauthorized');
			}

			req.currentUser = currentUser;
			next();
		} else {
			// const accessToken = req.headers['access-token-from-line'] as string;

			if (!accessToken) {
				throw new ForbiddenError('access-token-from-line is missing');
			}

			const isVerified = await verifyId(accessToken);
			if (!isVerified) {
				throw new ForbiddenError(
					'access-token-from-line is not verified',
				);
			}

			const profile = await getProfile(accessToken);
			if (!profile) {
				throw new BadRequestError('profile not found');
			}

			let user = await User.findOne({ lineUserId: profile.userId });
			if (!user) {
				user = await User.create({
					lineUserId: profile.userId,
					avatarUrl: profile.pictureUrl,
					first_name: profile.displayName,
				});
			}

			req.currentUser = user?.toJSON() as any;
			next();
		}
	} catch (e) {
		if (e instanceof jwt.TokenExpiredError) {
			next(new TokenExpiredError('token_expired'));
		} else if (e instanceof jwt.JsonWebTokenError) {
			next(new TokenInvalidError('token_invalid'));
		} else {
			next(e);
		}
	}
};

const verifyId = async (token: string) => {
	try {
		const oauth2 = await axios(
			`https://api.line.me/oauth2/v2.1/verify?access_token=${token}`,
			{
				method: 'GET',
				headers: {
					Authorization: `Bearer ${token}`,
					Accept: 'application/json',
				},
			},
		);
		if (!oauth2.data) {
			throw new BadRequestError('Invalid token');
		}
		if (oauth2.data?.client_id != process.env.LIFF_CHANNEL_ID) {
			throw new BadRequestError(
				`client_id mismatch ${oauth2.data?.client_id} != ${process.env.LIFF_CHANNEL_ID}`,
			);
		}
		if (oauth2.data?.expires_in <= 0) {
			throw new BadRequestError(`expired ${oauth2.data?.expires_in} < 0`);
		}
		return true;
	} catch (error: unknown) {
		const message =
			((error as any)?.response.data.error_description as string) ||
			(error as Error).message ||
			(error as string);
		throw new BadRequestError(message);
	}
};

const getProfile = async (token: string): Promise<any | null> => {
	try {
		const profile = await axios('https://api.line.me/v2/profile', {
			method: 'GET',
			headers: {
				Authorization: `Bearer ${token}`,
				Accept: 'application/json',
			},
		});
		if (!profile.data) {
			throw new BadRequestError('Invalid token');
		}
		return profile?.data as Promise<any>;
	} catch (error: unknown) {
		const message =
			((error as any)?.response.data.error_description as string) ||
			(error as Error).message ||
			(error as string);
		throw new BadRequestError(message);
	}
};
