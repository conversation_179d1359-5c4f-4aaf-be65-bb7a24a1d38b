import { Job, Queue, Worker } from 'bullmq';
import { createTransport, Transporter } from 'nodemailer';
import { ISendMailOptions } from './interfaces';
import env from '../../env';

export class MailerService {
  private static instance: MailerService;
  private readonly transport: Transporter;
  private readonly queue: Queue<ISendMailOptions>;

  constructor() {
    this.transport = createTransport(
      {
        host: env.mail.host,
        port: env.mail.port,
        auth: {
          user: env.mail.user,
          pass: env.mail.pass,
        },
      },
      { from: env.mail.from },
    );
    this.queue = new Queue('mail-sender', {
      connection: {
        host: env.redis.host,
        port: env.redis.port,
      },
    });
    this.createWorker();
  }

  createWorker() {
    const worker = new Worker(
      'mail-sender',
      async (job: Job<ISendMailOptions>) => {
        try {
          await this.sendMailSync(job.data);
        } catch (e) {
          console.log(e);
          throw Worker.RateLimitError();
        }
      },
      {
        connection: {
          host: env.redis.host,
          port: env.redis.port,
        },
        concurrency: env.mail.concurrency,
        limiter: env.mail.limiter,
      },
    );
    worker.on('completed', (job) => {
      console.log(`Job ${job.name} completed: ${job.id}`);
    });
    worker.on('failed', (job, err) => {
      console.error(`Job ${job?.name} failed with error: ${err.message}`);
    });
  }

  async sendMail(options: ISendMailOptions) {
    await this.queue.add('send-mail', options);
  }

  async sendMailSync(options: ISendMailOptions) {
    await this.transport.sendMail(options);
  }

  static getInstance() {
    if (!this.instance) {
      this.instance = new this();
    }
    return this.instance;
  }

  static async sendMail(options: ISendMailOptions) {
    await this.getInstance().sendMail(options);
  }

  static async sendMailSync(options: ISendMailOptions) {
    await this.getInstance().sendMailSync(options);
  }
}
