import compression from 'compression';
import cors from 'cors';
import express, { Response } from 'express';
import helmet from 'helmet';
import morgan from 'morgan';
import env from '@env';
import path from 'path';
import i18n from 'i18n';
import cookieParser from 'cookie-parser';
import { verifyTokenSystem } from '@middlewares/auth.middlewares';
export class ApplicationConfig {
	public static init(application: express.Application): void {
		// --- Middle wares
		application.use(express.json({ limit: env.app.bodyPayloadLimit }));
		application.use(
			express.urlencoded({
				limit: env.app.bodyPayloadLimit,
				extended: true,
			}),
		);
		application.use(express.raw({ limit: env.app.bodyPayloadLimit }));

		application.use(helmet());
		application.use(compression());
		application.use(cors());
		application.use(cookieParser(env.app.cookieSecret));

		i18n.configure({
			locales: ['jp', 'en'],
			directory: path.join(process.cwd(), 'src', 'locales'),
			defaultLocale: 'jp',
			header: 'Accept-Language',
			updateFiles: false,
		});

		application.use(i18n.init);

		const staticOptions = {
			setHeaders: (res: Response) => {
				res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
			},
		};

		application.use(
			'/uploads',
			verifyTokenSystem,
			express.static(path.join(process.cwd(), 'uploads'), staticOptions),
		);

		application.use((req, res, next) => {
			const preferredLocale = req.headers['Accept-Language'] || 'jp';
			req.setLocale(preferredLocale as string);
			next();
		});

		if (env.app.debugLog) {
			application.use(
				morgan('dev', {
					skip: (req, res) => {
						return res.statusCode < 400;
					},
					stream: process.stderr,
				}),
			);

			application.use(
				morgan('dev', {
					skip: (req, res) => {
						return res.statusCode >= 400;
					},
					stream: process.stdout,
				}),
			);
		}
	}
}
