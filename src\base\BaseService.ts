import { IPagination } from '@interfaces/IPagination';
import { BadRequestError, NotFoundError } from 'common';
import { DeepPartial, DeleteResult, FindManyOptions, FindOneOptions, FindOptionsWhere, Repository, UpdateResult } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { BaseEntity } from './BaseEntity';

export class BaseService<T extends BaseEntity> {
	constructor(protected repository: Repository<T>) { }

	async count(filter?: FindManyOptions<T>): Promise<number> {
		return await this.repository.count(filter);
	}

	async findAll(filter?: FindManyOptions<T>): Promise<IPagination<T>> {
		const [total_items, items] = await Promise.all([
			this.repository.count(filter),
			this.repository.find(filter)
		]);
		return { items, total_items };
	}

	async isExisted(filter: FindOneOptions<T>): Promise<T | null> {
		const record = await this.repository.findOne(filter);

		return record;
	}

	async findOne(options: FindOneOptions<T>, errorMessage?: string, errorCode?: string): Promise<T> {
		const record = await this.repository.findOne(options);
		if (!record) {
			throw new NotFoundError(errorMessage || `The requested record was not found`, errorCode);
		}
		return record;
	}

	async create(entity: DeepPartial<T>): Promise<T> {
		const record = this.repository.create(entity);
		await this.repository.save(record);
		return record;
	}

	async createBulk(entity: DeepPartial<T>[]): Promise<T[]> {
		const record = this.repository.create(entity);
		await this.repository.save(record);
		return record;
	}


	async update(id: string, entity: QueryDeepPartialEntity<T>): Promise<T | UpdateResult> {
		try {
			return await this.repository.update(id, entity);
		} catch (err /*: WriteError*/) {
			throw new BadRequestError(`The record was not found ${err}`);
		}
	}

	async delete(criteria: string | number | FindOptionsWhere<T>): Promise<DeleteResult> {
		try {
			return await this.repository.delete(criteria);
		} catch (err) {
			throw new NotFoundError(`The record was not found ${err}`);
		}
	}

	async getOne(filter: FindOneOptions<T>): Promise<T | null> {
		const record = (await this.repository.findOne(filter)) || null;
		return record;
	}

	async getAll(filter: FindOneOptions<T>): Promise<T[]> {
		const record = (await this.repository.find(filter)) || null;
		return record;
	}
}
