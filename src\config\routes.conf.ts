import { AuthRouter } from '@api/auth/v1/auth.route';
import { WebhookRouter } from '@api/webhook/v1/webhook.route';
import express from 'express';
import { SysRouter } from './sys.route';

export class Routes {
	public static init(app: express.Application, router: express.Router): void {
		// user router
		new SysRouter().init(router);
		new AuthRouter().init(router);
		new WebhookRouter().init(router);
		app.use('/api', router);
	}
}
