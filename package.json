{"name": "template-code", "version": "1.0.0", "description": "", "main": "index.ts", "engines": {"node": ">=18.0.0 <20.0.0"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "rm -rf ./dist && tsc", "tslint": "npx eslint . && -p tsconfig.json", "format": "prettier --write 'src/*.ts' 'src/**/*.ts'", "start": "node -r ./tsconfig-paths-bootstrap.js dist/index.js", "dev": "nodemon", "seed": "ts-node src/seed/seed.ts", "typeorm": "typeorm-ts-node-commonjs", "migration:create": "npm run typeorm -- migration:create", "migration:generate": "npm run typeorm -- migration:generate -d ./ormconfig.ts", "migration:run": "npm run typeorm -- migration:run -d ./ormconfig.ts", "migration:revert": "npm run typeorm -- migration:revert -d ./ormconfig.ts", "migration:show": "npm run typeorm -- migration:show -d ./ormconfig.ts"}, "repository": {"type": "git", "url": "*******************:javis/javis-code-template/nodejs/nodejs-large-template.git"}, "devDependencies": {"@eslint/js": "^9.2.0", "@types/bcrypt": "^5.0.0", "@types/bcryptjs": "^2.4.2", "@types/compression": "^1.7.2", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/helmet": "^4.0.0", "@types/i18n": "^0.13.6", "@types/jsonwebtoken": "^9.0.1", "@types/lodash": "^4.14.195", "@types/morgan": "^1.9.4", "@types/multer": "^1.4.7", "@types/node": "^18.14.2", "@types/nodemailer": "^6.4.9", "@types/otp-generator": "^4.0.2", "eslint": "^8.57.0", "nodemon": "^2.0.22", "prettier": "^2.1.2", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "tslint": "^6.1.3", "typescript": "^5.4.5", "typescript-eslint": "^7.8.0"}, "dependencies": {"aws-sdk": "2.1659.0", "axios": "^1.4.0", "bcrypt": "^5.1.0", "bcryptjs": "^2.4.3", "bullmq": "5.52.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "common": "file:libs/common-1.2.3.tgz", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.1.4", "express": "^4.18.2", "helmet": "^7.0.0", "i18n": "^0.15.1", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.21", "morgan": "^1.10.0", "nodemailer": "^6.9.4", "otp-generator": "^4.0.1", "payjp": "^2.2.0", "pg": "^8.16.3", "sharp": "^0.33.3", "typeorm": "^0.3.25", "winston": "^3.9.0"}, "author": "", "license": "ISC"}