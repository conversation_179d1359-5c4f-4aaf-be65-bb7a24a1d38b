import { Admin } from "@api/admin/admin.model";
import { BaseService } from "@base/BaseService";
import { AdminRepository } from "src/repositories/AdminRepository";

export default class AdminService extends BaseService<Admin> {
	constructor(
		protected adminRepository: AdminRepository,
	) {
		super(adminRepository);
	}

	static getInstance(): AdminService {
		const adminRepository = AdminRepository.getInstance();
		return new AdminService(adminRepository);
	}
}
