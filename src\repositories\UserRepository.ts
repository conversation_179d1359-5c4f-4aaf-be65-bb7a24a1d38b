import { User } from "@api/users/user.model";
import { appDataSource } from "ormconfig";
import { Repository } from "typeorm";

export class UserRepository extends Repository<User> {

	private static repository: Repository<User>;

	static getInstance(): Repository<User> {
		if (!UserRepository.repository) {
			UserRepository.repository = appDataSource.getRepository(User);
		}
		return UserRepository.repository;
	}
}
