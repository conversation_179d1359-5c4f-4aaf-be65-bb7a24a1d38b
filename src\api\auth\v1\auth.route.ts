import {
	ISignInInterface,
	ISignUpInterface
} from '@interfaces/authen.interface';

import { verifyTokenSystem } from '@middlewares/auth.middlewares';
import { validateBodyReq } from '@middlewares/validation.middlewares';
import {
	resOK,
	TokenExpiredError,
	TokenInvalidError
} from 'common';
import { NextFunction, Request, Response, Router } from 'express';
import jwt from 'jsonwebtoken';
import { UserRepository } from 'src/repositories/UserRepository';
import { AuthController } from './auth.controller';
import AuthService from './auth.service';
import { LoginRequest } from './request/login.request';
import { RefreshTokenRequest } from './request/refresh-token.request';
import { RegisterRequest } from './request/signup.request';
export class AuthRouter {
	private controller: AuthController;

	constructor() {
		const authService = AuthService.getInstance(
			UserRepository.getInstance()
		);

		this.controller = new AuthController(authService);
	}

	public init(router: Router): void {
		const authRouter = Router();
		authRouter
			.get('/me', verifyTokenSystem, this.getMe.bind(this))
			.post(
				'/login',
				validateBodyReq(LoginRequest),
				this.signIn.bind(this),
			)
			.post(
				'/register',
				validateBodyReq(RegisterRequest),
				this.signUp.bind(this),
			)
			.post(
				'/refresh_token',
				validateBodyReq(RefreshTokenRequest),
				this.refreshToken.bind(this),
			);
		router.use('/v1', authRouter);
	}

	private async refreshToken(
		req: Request,
		res: Response,
		next: NextFunction,
	): Promise<void> {
		try {
			const token = await this.controller.refreshToken(
				req.body.refresh_token,
			);
			res.status(200).json(resOK(token));
		} catch (error) {
			if (error instanceof jwt.TokenExpiredError) {
				next(new TokenExpiredError('token_expired'));
			} else if (error instanceof jwt.JsonWebTokenError) {
				next(new TokenInvalidError('token_invalid'));
			} else {
				next(error);
			}
		}
	}

	private async signIn(
		req: Request,
		res: Response,
		next: NextFunction,
	): Promise<void> {
		try {
			const bodyParams: ISignInInterface = {
				email: req.body.email,
				password: req.body.password,
			};
			const token = await this.controller.signIn(bodyParams);
			res.status(200).json(resOK(token));
		} catch (error) {
			next(error);
		}
	}

	private async getMe(req: Request, res: Response, next: NextFunction) {
		try {
			const user = await this.controller.getMe(req.currentUser.id);
			res.status(200).json(resOK(user));
		} catch (error) {
			next(error);
		}
	}

	private async signUp(
		req: Request,
		res: Response,
		next: NextFunction,
	): Promise<void> {
		try {
			const { password, email, first_name, last_name, phone_number } =
				req.body;
			const bodyParams: ISignUpInterface = {
				password,
				email,
				first_name,
				last_name,
				phone_number,
			};
			const user = await this.controller.signUp(bodyParams);
			res.status(200).json(resOK({ ...user }));
		} catch (error) {
			next(error);
		}
	}

}
