import { User } from '@api/users/user.model';
import { PERMISSION_TYPE } from '@constances/enum';
import { ISignInInterface, ISignUpInterface, IToken } from '@interfaces/authen.interface';
import { BadRequestError, EncUtil, NotAuthorizedError } from 'common';
import AuthService from './auth.service';
export class AuthController {

	constructor(
		private authService: AuthService,
	) {
		this.authService = authService;
	}

	async signIn(signInBody: ISignInInterface): Promise<IToken> {
		return await this.authService.signIn(signInBody);
	}

	async signUp(signUpBody: ISignUpInterface): Promise<IToken> {
		const existingUser = await this.authService.findOne({
			where: { email: signUpBody.email },
		});
		if (existingUser) {
			throw new BadRequestError('email_already_exists');
		}

		const user = await this.authService.create({
			...signUpBody,
			password: await EncUtil.createHash(signUpBody.password),
		});

		const token = this.authService.generateToken({
			id: user.id,
			permission: PERMISSION_TYPE.User,
		});

		return {
			access_token: token.access_token,
			refresh_token: token.refresh_token,
		};
	}

	async getMe(id: number): Promise<User | null> {
		return await this.authService.findOne({
			where: { id }
		});
	}

	async refreshToken(refreshToken: string): Promise<IToken> {
		const payload = this.authService.decode(refreshToken);
		if (!payload || !payload.id) {
			throw new NotAuthorizedError('unauthorized');
		}

		const user = await this.authService.findOne({ where: { id: payload.id } });
		if (!user || user.locked) {
			throw new NotAuthorizedError('unauthorized');
		}
		const tokens = this.authService.refreshToken(refreshToken);
		return {
			access_token: tokens.access_token,
			refresh_token: tokens.refresh_token,
		};
	}

}
