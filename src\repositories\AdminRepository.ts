import { Admin } from '@api/admin/admin.model';
import { appDataSource } from 'ormconfig';
import { Repository } from 'typeorm';

export class AdminRepository {
	private repository: Repository<Admin>;

	constructor() {
		this.repository = appDataSource.getRepository(Admin);
	}

	findByEmail(email: string) {
		return this.repository.findOne({ where: { email } });
	}

	// static getInstance(): Repository<Admin> {
	// 	if (!AdminRepository.repository) {
	// 		AdminRepository.repository = appDataSource.getRepository(Admin);
	// 	}
	// 	return AdminRepository.repository;
	// }
}
