import { Admin } from '@api/admin/admin.model';
import { Logger, EncUtil } from 'common';
import { AdminRepository } from 'src/repositories/AdminRepository';

export async function seed() {
	try {
		const password = process.env.PASSWORD_ADMIN || 'admin@123';

		// const checkAdmin = await Admin.findOne({
		// 	email: process.env.EMAIL_ADMIN || '<EMAIL>',
		// });

		let checkAdmin = await AdminRepository.findByEmail(
			process.env.EMAIL_ADMIN,
		);
		if (!checkAdmin) {
			const newAdmin = new Admin();
			newAdmin.email = process.env.EMAIL_ADMIN || '<EMAIL>';
			newAdmin.password = await EncUtil.createHash(password);
			await AdminRepository.getInstance().save(newAdmin);

			Logger.info('Seeder successfully');
		} else {
			Logger.info('Already seeded');
		}
	} catch (error) {
		Logger.error('Seeder failed!');
		throw error;
	}
}
