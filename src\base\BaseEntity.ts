import { CreateDateColumn, DeleteDate<PERSON><PERSON>umn, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

export interface IBaseEntityModel {
    id: number;
    deletedAt?: Date | null;
    createdAt: Date;
    updatedAt: Date;
}

export abstract class BaseEntity implements IBaseEntityModel {
    @PrimaryGeneratedColumn()
    id!: number;

    @CreateDateColumn()
    public createdAt!: Date;

    @UpdateDateColumn()
    public updatedAt!: Date;

    @DeleteDateColumn()
    public deletedAt!: Date;

}
