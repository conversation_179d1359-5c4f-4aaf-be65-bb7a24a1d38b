import sharp from 'sharp';
import fs from 'fs';
import path from 'path';
import { Request, Response, NextFunction } from 'express';
import { DEVICE_SIZES, IMAGE_COMPRESSION } from '@constances/constant';
import { BadRequestError } from 'common';
import { resizeAndSaveImage } from '@utilities/resize-save-img.util';

// Middleware để xử lý compressed ảnh

export const processImageMiddleware = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	const files = req.files as Express.Multer.File[];

	try {
		for (const file of files) {
			if (!file.mimetype.startsWith('image/')) {
				continue; // bỏ qua nếu không phải ảnh
			}

			const originalPath = file.path;
			const ext = path.extname(originalPath);
			const processedPath = originalPath.replace(ext, `-processed.jpeg`);

			try {
				const stats = fs.statSync(originalPath);
				const fileSizeInBytes = stats.size;

				// <PERSON><PERSON><PERSON> tra kích thước tệp
				const {
					THRESHOLD,
					HIGH_THRESHOLD,
					DEFAULT_QUALITY,
					LOW_QUALITY,
				} = IMAGE_COMPRESSION;

				if (fileSizeInBytes > THRESHOLD) {
					const quality =
						fileSizeInBytes > HIGH_THRESHOLD
							? LOW_QUALITY
							: DEFAULT_QUALITY;

					// Nén và chuyển đổi ảnh
					await sharp(originalPath)
						.toFormat('jpeg')
						.jpeg({ quality, progressive: true })
						.toFile(processedPath);

					for (const [device, size] of Object.entries(DEVICE_SIZES)) {
						const resizePath = originalPath.replace(
							ext,
							`-${device}-${size.width}x${size.height}${ext}`,
						);
						await resizeAndSaveImage(
							originalPath,
							resizePath,
							size.width,
							size.height,
						);
					}

					await fs.promises.rename(processedPath, originalPath);
					file.path = processedPath;
				}
			} catch (err) {
				console.error('Error processing file:', err);
				throw new BadRequestError('Error processing image.');
			}
		}
		next();
	} catch (err) {
		next(err);
	}
};
