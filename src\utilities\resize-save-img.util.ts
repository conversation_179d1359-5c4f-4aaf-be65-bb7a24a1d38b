import sharp from 'sharp';
import { IMAGE_COMPRESSION } from '@constances/constant';
import { BadRequestError } from 'common';

export const resizeAndSaveImage = async (
	originalPath: string,
	processedPath: string,
	width: number,
	height: number,
) => {
	try {
		// Resize ảnh cho các thiết bị với kích thước tương ứng
		await sharp(originalPath)
			.resize(width, height, {
				fit: 'inside', // <PERSON><PERSON><PERSON> bảo ảnh được resize mà không cắt
				withoutEnlargement: true, // Không phóng đại ảnh nếu kích thước nhỏ hơn yêu cầu
			})
			.toFormat('jpeg')
			.jpeg({
				quality: IMAGE_COMPRESSION.DEFAULT_QUALITY,
				progressive: true,
			})
			.toFile(processedPath);
	} catch (err) {
		console.error(`Error resizing image to ${width}x${height}:`, err);
		throw new BadRequestError(
			`Error resizing image to ${width}x${height}.`,
		);
	}
};
