import { <PERSON><PERSON><PERSON>, <PERSON>N<PERSON>Empty, IsString, Length } from 'class-validator';

export class CreateUserRequest {
	@IsNotEmpty({ message: 'email_is_a_required_field' })
	@IsEmail({}, { message: 'email_address_is_invalid' })
	email!: string;

	@IsNotEmpty({ message: 'password_is_a_required_field' })
	@IsString()
	@Length(6, 50, { message: 'please_enter_between_6_and_50_characters' })
	password!: string;

	@IsNotEmpty({ message: 'address_is_a_required_field' })
	address!: string;

	@IsNotEmpty({ message: 'phone_number_is_a_required_field' })
	phone_number!: string;

	constructor(req: CreateUserRequest) {
		Object.assign(this, req);
	}
}
