
### Use underscores for endpoint resful api
Use underscores for endpoint resful api. For example:

```
GET /api/v1/users
POST /api/v1/reset_password
```

### Use underscores for field in database

### Use Camel Case
Use camel case for variable and function names. Start with a lowercase letter and capitalize the first letter of each subsequent word. Do not use underscores. For example:

```
let myVariableName = "Example";
function myFunctionName() { ... }
```

### Use Pascal Case for Class Names
Use Pascal case for class names. Start with an uppercase letter and capitalize the first letter of each subsequent word. Do not use underscores. For example:

```
class MyClass { ... }
```

### Use underscores for private variables
Use underscores for private variables. For example:

```
let _myPrivateVariable = "Example";
```

### Whitespaces and Indentation
- Use 4 spaces for indentation.

### Use Semicolons
- Always use semicolons.

### Use Single Quotes
- Use single quotes for strings except to avoid escaping.

### Use prettier for formatting
```
{
    "semi": true,
    "trailingComma": "all",
    "singleQuote": true,
    "printWidth": 80,
    "tabWidth": 4,
    "useTabs": true
}
```
